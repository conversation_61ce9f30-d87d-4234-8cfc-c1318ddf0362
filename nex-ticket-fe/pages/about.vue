<script setup lang="ts">
import { FavouriteIcon, GlobalIcon, KnightShieldIcon, Search02Icon, SparklesIcon, WavingHand01Icon } from 'hugeicons-vue'

const { t } = useI18n()
</script>

<template>
  <div class="text-slate-800 overflow-hidden md:overflow-visible flex flex-col lg:gap-40 md:gap-32 gap-16 justify-center items-center p-4">
    <!-- Hero -->
    <section class="w-full max-w-[57.188rem] py-16 md:py-20 lg:py-28 text-center px-4">
      <div class="container mx-auto flex flex-col content-center items-center">
        <div>
          <h1
            class="lg:text-8xl md:text-7xl text-5xl lg:mb-4 md:mb-3 mb-2
                   font-sofia text-pie-700 font-[800]
                   lg:spacing-desktop-5 md:spacing-desktop-4 spacing-mobile-3"
          >
            {{ t('info.aboutUs.hero.title') }}
          </h1>
          <p
            class="lg:text-xl-medium md:text-lg-medium text-base-medium
                 text-slate-700 max-w-3xl mx-auto mb-6 md:mb-8 whitespace-pre-line"
          >
            {{ t('info.aboutUs.hero.subtitle') }}
          </p>
        </div>
        <div class="flex gap-4 md:gap-6 flex-col md:flex-row w-full md:w-auto">
          <!-- Desktop/Tablet -->
          <NexButton
            variant="primary"
            text-key="info.aboutUs.buttons.getStartedForFree"
            to="/registration.vue"
            :second-border="true"
            :paddingx="9"
            :paddingy="6"
            class="hidden md:flex"
          />
          <NexButton
            variant="secondary"
            text-key="info.aboutUs.buttons.contactSales"
            :append-icon="WavingHand01Icon"
            :paddingx="9"
            :paddingy="6"
            class="hidden md:flex"
          />

          <!-- Mobile -->
          <NexButton
            variant="primary"
            text-key="info.aboutUs.buttons.getStartedForFree"
            :second-border="true"
            :paddingx="6"
            :paddingy="4"
            class="md:hidden"
          />
          <NexButton
            variant="secondary"
            text-key="info.aboutUs.buttons.contactSales"
            :append-icon="WavingHand01Icon"
            :paddingx="6"
            :paddingy="4"
            class="md:hidden"
          />
        </div>
      </div>
    </section>

    <!-- Mission -->
    <section class="md:pb-9">
      <div
        class="lg:w-[120vw] lg:-ml-[10vw] lg:rotate-[2.5deg] lg:py-40 lg:px-0 lg:gap-12
               md:w-[110vw] md:-ml-[5vw] md:rotate-[1.5deg] md:py-32 md:px-0 md:gap-8
               w-screen flex items-center justify-center gap-3 bg-pie-700 py-12 px-4"
      >
        <div class="lg:w-[80rem] md:w-[70rem] w-full max-w-[22.5rem] md:max-w-none">
          <div class="flex flex-col gap-4 md:gap-6">
            <div class="px-4 md:px-6 lg:px-0">
              <p
                class="lg:text-2xl md:text-xl text-base
                       font-sofia text-slate-300 font-[800]
                       lg:spacing-desktop-2 md:spacing-desktop-2 spacing-mobile-2"
              >
                {{ t('info.aboutUs.mission.title') }}
              </p>
              <p
                class="lg:text-4xl-medium md:text-3xl-medium text-xl-medium
                       text-white"
              >
                “{{ t('info.aboutUs.mission.description') }}“
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <section class="w-full max-w-[90rem] mx-auto px-4">
      <div class="flex items-center justify-center rounded-[2rem] md:rounded-[3rem] border border-slate-700 lg:py-48 md:py-36 py-12 px-4 md:px-6 lg:px-0 bg-white">
        <div class="w-full max-w-[80rem] flex flex-col justify-center text-center items-center gap-3">
          <!-- Desktop/Tablet -->
          <h1
            class="lg:text-8xl md:text-7xl text-4xl
                   lg:spacing-desktop-5 md:spacing-desktop-4 spacing-mobile-3
                   font-sofia font-[800] text-pie-700 hidden md:block"
          >
            {{ t('info.aboutUs.values.title') }}
          </h1>

          <p class="lg:text-xl-medium md:text-lg-medium text-slate-500 hidden md:block">
            {{ t('info.aboutUs.values.description') }}
          </p>

          <!-- Mobile -->
          <h1 class="md:hidden text-4xl font-sofia font-[800] text-pie-700 spacing-mobile-3">
            {{ t('info.aboutUs.values.mobileTitle') }}
          </h1>

          <p class="text-sm-medium text-slate-700 md:hidden">
            {{ t('info.aboutUs.values.mobileDescription') }}
          </p>

          <div class="flex md:flex-row flex-col gap-4 md:gap-6 lg:mt-20 md:mt-16 mt-8">
            <!-- Simplicity -->
            <div
              class="lg:min-w-[15.875rem] lg:h-[15.125rem] lg:pt-9 lg:pb-7 lg:px-9 lg:gap-9
                     md:min-w-[13rem] md:h-[13rem] md:pt-6 md:pb-5 md:px-6 md:gap-6
                     gap-4 p-6 flex flex-col flex-1 min-w-0 rounded-2xl border border-slate-700 shadow-sm bg-slate-100"
            >
              <SparklesIcon :size="36" class="text-pie-700 md:w-8 md:h-8 lg:w-9 lg:h-9" />
              <div class="text-left flex flex-col gap-2">
                <p
                  class="lg:text-2xl-bold md:text-xl-bold text-lg-bold text-slate-700"
                >
                  {{ t('info.aboutUs.values.simplicity.title') }}
                </p>
                <p class="lg:text-sm-medium md:text-xs-medium text-xs-medium text-slate-500">
                  {{ t('info.aboutUs.values.simplicity.description') }}
                </p>
              </div>
            </div>

            <!-- Transparency -->
            <div
              class="lg:min-w-[15.875rem] lg:h-[15.125rem] lg:pt-9 lg:pb-7 lg:px-9 lg:gap-9
                     md:min-w-[13rem] md:h-[13rem] md:pt-6 md:pb-5 md:px-6 md:gap-6
                     gap-4 p-6 flex flex-col flex-1 min-w-0 rounded-2xl border border-slate-700 shadow-sm bg-slate-100"
            >
              <Search02Icon :size="36" class="text-pie-700 md:w-8 md:h-8 lg:w-9 lg:h-9" />
              <div class="text-left flex flex-col gap-2">
                <p
                  class="lg:text-2xl-bold md:text-xl-bold text-lg-bold text-slate-700"
                >
                  {{ t('info.aboutUs.values.transparency.title') }}
                </p>
                <p class="lg:text-sm-medium md:text-xs-medium text-xs-medium text-slate-500">
                  {{ t('info.aboutUs.values.transparency.description') }}
                </p>
              </div>
            </div>

            <!-- Community -->
            <div
              class="lg:min-w-[15.875rem] lg:h-[15.125rem] lg:pt-9 lg:pb-7 lg:px-9 lg:gap-9
                     md:min-w-[13rem] md:h-[13rem] md:pt-6 md:pb-5 md:px-6 md:gap-6
                     gap-4 p-6 flex flex-col flex-1 min-w-0 rounded-2xl border border-slate-700 shadow-sm bg-slate-100"
            >
              <GlobalIcon :size="36" class="text-pie-700 md:w-8 md:h-8 lg:w-9 lg:h-9" />
              <div class="text-left flex flex-col gap-2">
                <p
                  class="lg:text-2xl-bold md:text-xl-bold text-lg-bold text-slate-700"
                >
                  {{ t('info.aboutUs.values.community.title') }}
                </p>
                <p class="lg:text-sm-medium md:text-xs-medium text-xs-medium text-slate-500">
                  {{ t('info.aboutUs.values.community.description') }}
                </p>
              </div>
            </div>
          </div>

          <div class="flex md:flex-row flex-col gap-4 md:gap-6 w-full">
            <!-- Passion -->
            <div
              class="lg:min-w-[15.875rem] lg:h-[15.125rem] lg:pt-9 lg:pb-7 lg:px-9 lg:gap-9
                     md:min-w-[13rem] md:h-[13rem] md:pt-6 md:pb-5 md:px-6 md:gap-6
                     gap-4 p-6 flex flex-col flex-1 min-w-0 rounded-2xl border border-slate-700 shadow-sm bg-slate-100"
            >
              <FavouriteIcon :size="36" class="text-pie-700 md:w-8 md:h-8 lg:w-9 lg:h-9" />
              <div class="text-left flex flex-col gap-2">
                <p
                  class="lg:text-2xl-bold md:text-xl-bold text-lg-bold text-slate-700"
                >
                  {{ t('info.aboutUs.values.passion.title') }}
                </p>
                <p class="lg:text-sm-medium md:text-xs-medium text-xs-medium text-slate-500">
                  {{ t('info.aboutUs.values.passion.description') }}
                </p>
              </div>
            </div>

            <!-- Reliability -->
            <div
              class="lg:min-w-[15.875rem] lg:h-[15.125rem] lg:pt-9 lg:pb-7 lg:px-9 lg:gap-9
                     md:min-w-[13rem] md:h-[13rem] md:pt-6 md:pb-5 md:px-6 md:gap-6
                     gap-4 p-6 flex flex-col flex-1 min-w-0 rounded-2xl border border-slate-700 shadow-sm bg-slate-100"
            >
              <KnightShieldIcon :size="36" class="text-pie-700 md:w-8 md:h-8 lg:w-9 lg:h-9" />
              <div class="text-left flex flex-col gap-2">
                <p
                  class="lg:text-2xl-bold md:text-xl-bold text-lg-bold text-slate-700"
                >
                  {{ t('info.aboutUs.values.reliability.title') }}
                </p>
                <p class="lg:text-sm-medium md:text-xs-medium text-xs-medium text-slate-500">
                  {{ t('info.aboutUs.values.reliability.description') }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <section class="flex justify-center w-full px-4">
      <div class="flex flex-col md:flex-row lg:py-12 md:py-8 py-0 w-full max-w-[80rem] lg:gap-[6.313rem] md:gap-12 gap-9 items-center justify-center">
        <img
          class="shrink-0 rounded-2xl w-full max-w-[20.5rem] h-[26.313rem]
                 md:w-[18rem] md:h-[28rem] lg:w-[22.438rem] lg:h-[34.438rem]
                 object-cover bg-slate-200"
          src="assets/images/marek_pospisil.jpg"
          alt="Placeholder"
        >
        <div class="flex flex-col gap-3 md:gap-4 w-full md:flex-1">
          <p class="md:text-4xl-medium text-xl-medium text-slate-900 before:content-['“'] after:content-['”']">
            <span class="font-sofia font-extrabold text-pie-700">
              TICKETPIE
            </span>
            <span>
              {{ t('info.aboutUs.story.paragraph1') }}
            </span>
          </p>
          <div>
            <p class="md:text-2xl-bold text-sm-bold text-slate-900">
              {{ t('info.aboutUs.story.names') }}
            </p>
            <p class="md:text-2xl-medium text-sm-normal text-slate-500">
              {{ t('info.aboutUs.story.founders') }}
            </p>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<style scoped>
@media (min-width: 768px) {
  .spacing-desktop-5 {
    letter-spacing: -0.05em;
  }
  .spacing-desktop-2{
    letter-spacing: -0.02em;
  }
  .spacing-desktop-3{
    letter-spacing: -0.03em;
  }
}

@media (max-width: 768px) {
  .spacing-mobile-5 {
    letter-spacing: -0.05em;
  }
  .spacing-mobile-2{
    letter-spacing: -0.02em;
  }
  .spacing-mobile-3{
    letter-spacing: -0.03em;
  }
}
</style>
